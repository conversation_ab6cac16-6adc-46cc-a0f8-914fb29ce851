defmodule Mqttable.MqttProxy.StorageTest do
  use ExUnit.Case, async: true

  alias Mqttable.MqttProxy.Storage

  test "normalize from toxiproxy-like json" do
    m = %{
      "name" => "p1",
      "listen" => "127.0.0.1:20000",
      "upstream" => "broker:1883",
      "enabled" => true,
      "toxics" => %{"latency_ms" => 10, "jitter_ms" => 5}
    }

    norm = Storage.normalize(m)

    assert norm.name == "p1"
    assert norm.listen_port == 20_000
    assert norm.upstream_host == "broker"
    assert norm.toxics[:latency_ms] == 10
    assert norm.toxics[:jitter_ms] == 5
  end
end
