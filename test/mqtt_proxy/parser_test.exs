defmodule Mqttable.MqttProxy.ParserTest do
  use ExUnit.Case, async: true

  alias Mqttable.MqttProxy.Parser

  test "parse fixed header" do
    # PUBLISH QoS 1 retain=0 => type=3 (0x3), qos bits 01, retain 0
    # first byte: 0b0011 0010 = 0x32
    <<first, _::binary>> = <<0x32, 0x00, 0x00, 0x00>>
    {type, qos, retain} = Parser.parse_fixed_header(<<first, 0x00>>)
    assert type == "PUBLISH"
    assert qos == "1"
    refute retain
  end
end

