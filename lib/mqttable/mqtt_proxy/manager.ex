defmodule Mqttable.MqttProxy.Manager do
  @moduledoc """
  DynamicSupervisor that manages MQTT Proxy listener workers.

  Each proxy has a unique name and listens on a TCP port, forwarding bytes to a configured upstream
  MQTT broker. Network simulation (latency, jitter, loss, bandwidth) can be adjusted at runtime.

  Emits trace messages compatible with TraceManager so existing TraceGrid UI can display flows.
  """

  use DynamicSupervisor
  require Logger

  alias Mqttable.MqttProxy.{Storage, Worker}

  @type start_opts :: [
          name: String.t(),
          listen_host: String.t(),
          listen_port: pos_integer(),
          upstream_host: String.t(),
          upstream_port: pos_integer(),
          enabled: boolean(),
          toxics: map()
        ]

  def start_link(opts \\ []) do
    DynamicSupervisor.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @impl true
  def init(_opts) do
    :ets.new(__MODULE__, [:set, :public, :named_table])
    # Load proxies asynchronously after supervisor starts
    Task.start(fn -> load_and_start_all() end)
    DynamicSupervisor.init(strategy: :one_for_one)
  end

  # Public API

  @spec load_and_start_all() :: :ok | {:error, term()}
  def load_and_start_all do
    case Storage.load() do
      {:ok, proxies} ->
        Enum.each(proxies, fn p ->
          if Map.get(p, :enabled, true), do: start_proxy(p)
        end)

        :ok

      {:error, reason} ->
        Logger.error("Failed to load proxies: #{reason}")
        {:error, reason}
    end
  end

  @spec list() :: [map()]
  def list do
    case Storage.load() do
      {:ok, proxies} -> proxies
      {:error, _} -> []
    end
  end

  @spec get(String.t()) :: map() | nil
  def get(name) do
    list() |> Enum.find(fn p -> p[:name] == name || p["name"] == name end)
  end

  @spec running?(String.t()) :: boolean()
  def running?(name) do
    match?([{^name, pid}] when is_pid(pid), :ets.lookup(__MODULE__, name))
  end

  @spec upsert(map()) :: {:ok, map()} | {:error, String.t()}
  def upsert(proxy) when is_map(proxy) do
    with {:ok, proxies} <- Storage.load(),
         normalized <- normalize_proxy_map(proxy),
         updated <- upsert_in_list(proxies, normalized),
         {:ok, _} <- Storage.save(updated) do
      # reflect runtime according to enabled flag
      maybe_apply_runtime_change(normalized)
      {:ok, normalized}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @spec start_proxy(map()) :: {:ok, pid()} | {:error, term()}
  def start_proxy(%{name: name} = proxy) do
    spec = {Worker, proxy}

    case DynamicSupervisor.start_child(__MODULE__, spec) do
      {:ok, pid} ->
        :ets.insert(__MODULE__, {name, pid})
        {:ok, pid}

      {:error, {:already_started, pid}} ->
        :ets.insert(__MODULE__, {name, pid})
        {:ok, pid}

      other ->
        other
    end
  end

  @spec stop_proxy(String.t()) :: :ok | {:error, :not_found}
  def stop_proxy(name) when is_binary(name) do
    case :ets.lookup(__MODULE__, name) do
      [{^name, pid}] ->
        DynamicSupervisor.terminate_child(__MODULE__, pid)

      _ ->
        {:error, :not_found}
    end
  end

  defp normalize_proxy_map(%{"name" => _} = m), do: normalize_json_proxy(m)
  defp normalize_proxy_map(%{name: _} = m), do: m

  defp normalize_json_proxy(m) do
    # local normalization mirroring Storage.normalize_proxy/1 but public
    listen = Map.fetch!(m, "listen")
    upstream = Map.fetch!(m, "upstream")
    {listen_host, listen_port} = split_host_port(listen)
    {upstream_host, upstream_port} = split_host_port(upstream)

    %{
      name: Map.fetch!(m, "name"),
      listen_host: listen_host,
      listen_port: listen_port,
      upstream_host: upstream_host,
      upstream_port: upstream_port,
      enabled: Map.get(m, "enabled", true),
      toxics: normalize_toxics(Map.get(m, "toxics", %{}))
    }
  end

  defp split_host_port(str) do
    case String.split(str, ":", parts: 2) do
      [host, port] -> {host, String.to_integer(port)}
      [only_port] -> {"127.0.0.1", String.to_integer(only_port)}
      _ -> {"127.0.0.1", 0}
    end
  end

  defp normalize_toxics(%{} = toxics) do
    %{
      enabled: Map.get(toxics, "enabled", Map.get(toxics, :enabled, true)),
      latency_ms: Map.get(toxics, "latency_ms", Map.get(toxics, :latency_ms, 0)),
      jitter_ms: Map.get(toxics, "jitter_ms", Map.get(toxics, :jitter_ms, 0)),
      loss_percent: Map.get(toxics, "loss_percent", Map.get(toxics, :loss_percent, 0)),
      bandwidth_kbps: Map.get(toxics, "bandwidth_kbps", Map.get(toxics, :bandwidth_kbps, 0))
    }
  end

  defp maybe_apply_runtime_change(%{name: name, enabled: enabled} = proxy) do
    cond do
      enabled and not running?(name) -> start_proxy(proxy)
      not enabled and running?(name) -> stop_proxy(name)
      true -> :ok
    end
  end

  @spec update_toxics(String.t(), map()) :: :ok | {:error, term()}
  def update_toxics(name, toxics) do
    # persist
    case Storage.load() do
      {:ok, proxies} ->
        updated =
          Enum.map(proxies, fn p ->
            if p.name == name do
              Map.update(p, :toxics, %{}, &Map.merge(&1, toxics))
            else
              p
            end
          end)

        _ = Storage.save(updated)

      _ ->
        :noop
    end

    # apply to runtime if running
    case call_worker(name, {:update_toxics, toxics}) do
      {:error, :not_found} -> :ok
      other -> other
    end
  end

  @spec enable(String.t(), boolean()) :: :ok | {:error, term()}
  def enable(name, enabled) do
    # persist 'enabled'
    _ =
      case Storage.load() do
        {:ok, proxies} ->
          new_list =
            Enum.map(proxies, fn p ->
              if p.name == name, do: Map.put(p, :enabled, enabled), else: p
            end)

          Storage.save(new_list)

        _ ->
          {:error, :storage}
      end

    cond do
      enabled and not running?(name) ->
        case get(name) do
          nil -> {:error, :not_found}
          proxy -> start_proxy(proxy) |> then(fn _ -> :ok end)
        end

      not enabled and running?(name) ->
        stop_proxy(name)
        :ok

      running?(name) ->
        call_worker(name, {:enable, enabled})

      true ->
        :ok
    end
  end

  defp call_worker(name, msg) do
    case :ets.lookup(__MODULE__, name) do
      [{^name, pid}] when is_pid(pid) -> GenServer.call(pid, msg)
      _ -> {:error, :not_found}
    end
  end

  defp upsert_in_list(list, %{name: name} = proxy) do
    case Enum.find_index(list, &(&1.name == name || &1["name"] == name)) do
      nil -> [proxy | list]
      idx -> List.replace_at(list, idx, proxy)
    end
  end
end
