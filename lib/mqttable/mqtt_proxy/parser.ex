defmodule Mqttable.MqttProxy.Parser do
  @moduledoc """
  Minimal MQTT fixed header parser for proxy tracing.

  Extracts packet type, qos, retain from the first byte quickly without full MQTT parsing.
  """

  import Bitwise

  @type parsed :: {
          type :: String.t(),
          qos :: String.t(),
          retain :: boolean()
        }

  @spec parse_fixed_header(binary()) :: parsed()
  def parse_fixed_header(<<type_flags, _rest::binary>>) do
    type = (type_flags >>> 4) &&& 0x0F
    qos_bits = (type_flags &&& 0b0000_0110) >>> 1
    retain = (type_flags &&& 0b0000_0001) == 1

    {packet_name(type), format_qos(qos_bits), retain}
  end

  def parse_fixed_header(_), do: {"UNKNOWN", "-", false}

  defp packet_name(1), do: "CONNECT"
  defp packet_name(2), do: "CONNACK"
  defp packet_name(3), do: "PUBLISH"
  defp packet_name(4), do: "PUBACK"
  defp packet_name(5), do: "PUBREC"
  defp packet_name(6), do: "PUBREL"
  defp packet_name(7), do: "PUBCOMP"
  defp packet_name(8), do: "SUBSCRIBE"
  defp packet_name(9), do: "SUBACK"
  defp packet_name(10), do: "UNSUBSCRIBE"
  defp packet_name(11), do: "UNSUBACK"
  defp packet_name(12), do: "PINGREQ"
  defp packet_name(13), do: "PINGRESP"
  defp packet_name(14), do: "DISCONNECT"
  defp packet_name(15), do: "AUTH"
  defp packet_name(_), do: "UNKNOWN"

  defp format_qos(0), do: "0"
  defp format_qos(1), do: "1"
  defp format_qos(2), do: "2"
  defp format_qos(_), do: "-"
end

