defmodule Mqttable.MqttProxy.Storage do
  @moduledoc """
  Persistence layer for MQTT Proxy configurations.

  Stores proxy definitions and their simulation settings in a JSON file under priv/data.
  Follows the same conventions as ConnectionSets.Storage for consistency.
  """

  require Logger

  @data_dir "priv/data"
  @proxies_file Path.join(@data_dir, "proxies.json")

  @type toxic_settings :: %{
          optional(:enabled) => boolean(),
          optional(:latency_ms) => non_neg_integer(),
          optional(:jitter_ms) => non_neg_integer(),
          optional(:loss_percent) => non_neg_integer(),
          optional(:bandwidth_kbps) => non_neg_integer()
        }

  @type proxy :: %{
          name: String.t(),
          listen_host: String.t(),
          listen_port: pos_integer(),
          upstream_host: String.t(),
          upstream_port: pos_integer(),
          enabled: boolean(),
          toxics: toxic_settings()
        }

  @spec load() :: {:ok, [proxy()]} | {:error, String.t()}
  def load do
    if File.exists?(@proxies_file) do
      with {:ok, json} <- File.read(@proxies_file),
           {:ok, raw} <- Jason.decode(json),
           proxies <- Enum.map(raw, &normalize_proxy/1) do
        {:ok, proxies}
      else
        {:error, %Jason.DecodeError{} = reason} ->
          {:error, "Failed to decode proxies JSON: #{inspect(reason)}"}

        {:error, reason} ->
          {:error, "Failed to read proxies file: #{reason}"}
      end
    else
      {:ok, []}
    end
  end

  @spec save([proxy()]) :: {:ok, String.t()} | {:error, String.t()}
  def save(proxies) when is_list(proxies) do
    File.mkdir_p!(@data_dir)

    proxies
    |> Enum.map(&prepare_for_persistence/1)
    |> Jason.encode(pretty: true)
    |> case do
      {:ok, json} ->
        case File.write(@proxies_file, json) do
          :ok -> {:ok, @proxies_file}
          {:error, reason} -> {:error, "Failed to write proxies file: #{reason}"}
        end

      {:error, reason} ->
        {:error, "Failed to encode proxies to JSON: #{inspect(reason)}"}
    end
  end

  # -- helpers --

  defp normalize_proxy(
         %{
           "name" => name,
           "listen" => listen,
           "upstream" => upstream
         } = m
       ) do
    {listen_host, listen_port} = split_host_port(listen)
    {upstream_host, upstream_port} = split_host_port(upstream)

    %{
      name: name,
      listen_host: listen_host,
      listen_port: listen_port,
      upstream_host: upstream_host,
      upstream_port: upstream_port,
      enabled: Map.get(m, "enabled", true),
      toxics: normalize_toxics(Map.get(m, "toxics", %{}))
    }
  end

  defp normalize_proxy(%{} = m) do
    # already normalized atom-key map
    m
    |> Map.update(:enabled, true, & &1)
    |> Map.update(:toxics, %{}, &normalize_toxics/1)
  end

  @doc """
  Public normalization helper primarily for tests and UI inputs.
  """
  @spec normalize(map()) :: map()
  def normalize(map) when is_map(map), do: normalize_proxy(map)

  defp normalize_toxics(%{} = toxics) do
    %{
      enabled: Map.get(toxics, "enabled", Map.get(toxics, :enabled, true)),
      latency_ms: Map.get(toxics, "latency_ms", Map.get(toxics, :latency_ms, 0)),
      jitter_ms: Map.get(toxics, "jitter_ms", Map.get(toxics, :jitter_ms, 0)),
      loss_percent: Map.get(toxics, "loss_percent", Map.get(toxics, :loss_percent, 0)),
      bandwidth_kbps: Map.get(toxics, "bandwidth_kbps", Map.get(toxics, :bandwidth_kbps, 0))
    }
  end

  defp split_host_port(str) when is_binary(str) do
    case String.split(str, ":", parts: 2) do
      [host, port] -> {host, String.to_integer(port)}
      [only_port] -> {"127.0.0.1", String.to_integer(only_port)}
      _ -> {"127.0.0.1", 0}
    end
  end

  defp prepare_for_persistence(%{
         name: name,
         listen_host: lhost,
         listen_port: lport,
         upstream_host: uhost,
         upstream_port: uport,
         enabled: enabled,
         toxics: toxics
       }) do
    %{
      name: name,
      listen: "#{lhost}:#{lport}",
      upstream: "#{uhost}:#{uport}",
      enabled: enabled,
      toxics: %{
        enabled: Map.get(toxics, :enabled, true),
        latency_ms: Map.get(toxics, :latency_ms, 0),
        jitter_ms: Map.get(toxics, :jitter_ms, 0),
        loss_percent: Map.get(toxics, :loss_percent, 0),
        bandwidth_kbps: Map.get(toxics, :bandwidth_kbps, 0)
      }
    }
  end
end
