defmodule Mqttable.MqttProxy.Worker do
  @moduledoc """
  A GenServer that listens on a TCP port and proxies MQTT traffic to an upstream broker.

  It implements network simulation features inspired by Toxiproxy:
  - latency/jitter (delay before forwarding)
  - packet loss (probabilistic drop)
  - bandwidth throttling (token-bucket per-direction)

  The worker accepts plain TCP (mqtt) for now; extension to TLS/WS can be added later.
  """

  use GenServer
  require Logger
  alias Phoenix.PubSub
  alias Mqttable.TraceManager
  alias Mqttable.MqttProxy.Parser

  @pubsub_topic "mqtt_trace"

  @type state :: %{
          name: String.t(),
          listen_host: String.t(),
          listen_port: pos_integer(),
          upstream_host: String.t(),
          upstream_port: pos_integer(),
          enabled: boolean(),
          toxics: map(),
          listener: port() | nil,
          acceptor: Task.t() | nil
        }

  @spec start_link(map()) :: GenServer.on_start()
  def start_link(opts) do
    GenServer.start_link(__MODULE__, opts)
  end

  @impl true
  def init(%{name: name} = opts) do
    state = %{
      name: name,
      listen_host: Map.fetch!(opts, :listen_host),
      listen_port: Map.fetch!(opts, :listen_port),
      upstream_host: Map.fetch!(opts, :upstream_host),
      upstream_port: Map.fetch!(opts, :upstream_port),
      enabled: Map.get(opts, :enabled, true),
      toxics: Map.get(opts, :toxics, %{}),
      listener: nil,
      acceptor: nil
    }

    {:ok, state, {:continue, :start_listener}}
  end

  @impl true
  def handle_continue(:start_listener, %{enabled: false} = state), do: {:noreply, state}

  def handle_continue(:start_listener, state) do
    {:ok, listener} =
      :gen_tcp.listen(state.listen_port, [
        :binary,
        packet: :raw,
        active: false,
        reuseaddr: true,
        ip: parse_ip(state.listen_host)
      ])

    {:ok, task} = Task.start(fn -> accept_loop(self(), listener) end)
    {:noreply, %{state | listener: listener, acceptor: task}}
  end

  @impl true
  def handle_call({:update_toxics, toxics}, _from, state) do
    {:reply, :ok, %{state | toxics: Map.merge(state.toxics, toxics)}}
  end

  def handle_call({:enable, enabled}, _from, state) do
    if enabled and not state.enabled do
      send(self(), :restart_listener)
    end

    {:reply, :ok, %{state | enabled: enabled}}
  end

  @impl true
  def handle_info(:restart_listener, state) do
    if state.listener do
      :gen_tcp.close(state.listener)
    end

    {:noreply, state, {:continue, :start_listener}}
  end

  defp accept_loop(server, listener) do
    case :gen_tcp.accept(listener) do
      {:ok, client} ->
        Task.start_link(fn -> handle_client(server, client) end)
        accept_loop(server, listener)

      {:error, :closed} ->
        :ok

      {:error, reason} ->
        Logger.error("Proxy accept failed: #{inspect(reason)}")
        :timer.sleep(100)
        accept_loop(server, listener)
    end
  end

  defp handle_client(server, client_sock) do
    state = :sys.get_state(server)

    with {:ok, upstream} <-
           :gen_tcp.connect(parse_ip(state.upstream_host), state.upstream_port, [
             :binary,
             packet: :raw,
             active: false
           ]) do
      spawn_link(fn -> pipe(state, client_sock, upstream, :downstream) end)
      spawn_link(fn -> pipe(state, upstream, client_sock, :upstream) end)
      :ok
    else
      {:error, reason} ->
        Logger.error("Failed to connect upstream: #{inspect(reason)}")
        :gen_tcp.close(client_sock)
    end
  end

  # Pipe bytes from src to dst applying toxics
  defp pipe(state, src, dst, direction) do
    case :gen_tcp.recv(src, 0) do
      {:ok, data} ->
        maybe_emit_trace(state, data, direction)

        if should_drop?(state.toxics) do
          pipe(state, src, dst, direction)
        else
          :ok = maybe_delay(state.toxics)
          :ok = maybe_throttle(state.toxics, byte_size(data))
          :ok = :gen_tcp.send(dst, data)
          pipe(state, src, dst, direction)
        end

      {:error, :closed} ->
        :gen_tcp.close(dst)

      {:error, reason} ->
        Logger.debug("Pipe error #{inspect(direction)}: #{inspect(reason)}")
        :gen_tcp.close(dst)
    end
  end

  defp maybe_emit_trace(state, data, direction) do
    # Try to parse one MQTT packet for metadata; on failure, still emit raw sizes
    timestamp_int = DateTime.to_unix(DateTime.utc_now(), :microsecond)

    {packet_type, topic, qos, retain, packet_id, properties, reason_code, extra_info} =
      parse_mqtt_metadata(data)

    trace_message = %{
      id: System.unique_integer([:positive, :monotonic]),
      broker_name: state.name,
      timestamp: timestamp_int,
      client_id: "proxy:#{state.name}",
      type: packet_type,
      direction: format_direction(direction),
      topic: topic,
      payload: extract_payload_preview(packet_type, data),
      data_size: byte_size(data),
      payload_size: byte_size(data),
      qos: qos,
      retain: retain,
      packet_id: packet_id,
      properties: properties,
      reason_code: reason_code,
      extra_info: extra_info
    }

    TraceManager.store_message(state.name, trace_message)
    PubSub.broadcast(Mqttable.PubSub, @pubsub_topic, {:mqtt_trace_message, trace_message})
  end

  defp format_direction(:upstream), do: "OUT"
  defp format_direction(:downstream), do: "IN"

  defp parse_mqtt_metadata(data) when is_binary(data) do
    {type, qos, retain} = Parser.parse_fixed_header(data)
    {type, "", qos, retain, nil, %{}, nil, nil}
  end

  defp extract_payload_preview("PUBLISH", data), do: truncate_binary(data, 100)
  defp extract_payload_preview(_, _), do: ""

  defp truncate_binary(bin, max) when byte_size(bin) > max do
    <<prefix::binary-size(max), _::binary>> = bin
    prefix
  end

  defp truncate_binary(bin, _), do: bin

  # toxics helpers
  defp should_drop?(%{enabled: true, loss_percent: p}) when is_integer(p) and p > 0 do
    :rand.uniform(100) <= min(p, 100)
  end

  defp should_drop?(_), do: false

  defp maybe_delay(%{enabled: true, latency_ms: l, jitter_ms: j}) do
    delay = max(l, 0) + if j && j > 0, do: :rand.uniform(j), else: 0
    if delay > 0, do: Process.sleep(delay), else: :ok
  end

  defp maybe_delay(_), do: :ok

  defp maybe_throttle(%{enabled: true, bandwidth_kbps: kbps}, size)
       when is_integer(kbps) and kbps > 0 do
    # crude throttle: sleep proportional to size/throughput
    bytes_per_ms = kbps * 1024 / 1000
    ms = Float.ceil(size / max(bytes_per_ms, 1.0)) |> trunc()
    if ms > 0, do: Process.sleep(ms), else: :ok
  end

  defp maybe_throttle(_, _), do: :ok

  defp parse_ip(host) when is_binary(host) do
    case :inet.parse_address(String.to_charlist(host)) do
      {:ok, ip} -> ip
      _ -> {127, 0, 0, 1}
    end
  end
end
