defmodule Mqttable.MqttClient.Worker do
  @moduledoc """
  Worker process for managing individual MQTT client connections.
  Each worker handles one MQTT client connection and manages its own reconnection logic.
  """
  use GenServer
  require Logger

  alias Phoenix.PubSub
  alias Mqttable.MqttClient.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ClientRegistry, Subscription}

  @pubsub_topic "mqtt_clients"
  @status_connecting :connecting
  @status_connected :connected
  @status_disconnected :disconnected
  @status_reconnecting :reconnecting

  # API Functions

  def start_link({broker, connection}) do
    {broker.name, connection.client_id}
    |> via_tuple()
    |> then(&GenServer.start_link(__MODULE__, {broker, connection}, name: &1))
  end

  def subscribe(broker_name, client_id, topic, opts) do
    {broker_name, client_id} |> locate_worker() |> execute_worker_call({:subscribe, topic, opts})
  end

  def unsubscribe(broker_name, client_id, topic) do
    {broker_name, client_id} |> locate_worker() |> execute_worker_call({:unsubscribe, topic})
  end

  def publish(broker_name, client_id, topic, payload, opts) do
    {broker_name, client_id}
    |> locate_worker()
    |> execute_worker_call({:publish, topic, payload, opts})
  end

  def retrieve_startup_args(broker_name, client_id) do
    {broker_name, client_id} |> locate_worker() |> execute_worker_call(:get_startup_args)
  end

  def disconnect(broker_name, client_id) do
    {broker_name, client_id} |> locate_worker() |> execute_worker_cast(:disconnect)
  end

  def synchronize_scheduled_messages(broker_name, client_id, scheduled_messages) do
    {broker_name, client_id}
    |> locate_worker()
    |> execute_worker_call({:sync_scheduled_messages, scheduled_messages})
  end

  def retrieve_debug_info(broker_name, client_id) do
    {broker_name, client_id} |> locate_worker() |> execute_worker_call(:debug_info)
  end

  def get_connection_info(broker_name, client_id) do
    {broker_name, client_id} |> locate_worker() |> execute_worker_call(:get_connection_info)
  end

  # Worker Location and Communication

  defp locate_worker({broker_name, client_id}) do
    case Registry.lookup(Mqttable.MqttClient.Registry, {broker_name, client_id}) do
      [{pid, _}] -> {:ok, pid}
      [] -> {:error, :not_found}
    end
  end

  defp execute_worker_call({:ok, worker_pid}, msg), do: GenServer.call(worker_pid, msg)
  defp execute_worker_call({:error, :not_found}, _msg), do: {:error, :not_connected}

  defp execute_worker_cast({:ok, worker_pid}, msg), do: GenServer.cast(worker_pid, msg)
  defp execute_worker_cast({:error, :not_found}, _msg), do: {:error, :not_connected}

  defp via_tuple({broker_name, client_id}) do
    {:via, Registry, {Mqttable.MqttClient.Registry, {broker_name, client_id}}}
  end

  # GenServer Callbacks

  @impl true
  def init({broker, connection}) do
    :erlang.process_flag(:trap_exit, true)

    {broker, connection}
    |> build_initial_state()
    |> register_client_as_connecting()
    |> then(&{:ok, &1, {:continue, :connect}})
  end

  @impl true
  def handle_continue(:connect, state), do: handle_info(:connect, state)

  @impl true
  def handle_call(:get_startup_args, _from, state) do
    {:reply, {:ok, {state.broker, state.connection}}, state}
  end

  def handle_call(:debug_info, _from, state) do
    state |> build_debug_info() |> then(&{:reply, {:ok, &1}, state})
  end

  def handle_call(:get_connection_info, _from, state) do
    connection_info = %{
      status: state.status,
      connection_time: state.connection_time,
      client_id: state.client_id,
      broker_name: state.broker.name
    }

    {:reply, {:ok, connection_info}, state}
  end

  def handle_call({:sync_scheduled_messages, scheduled_messages}, _from, state) do
    current_messages = Map.get(state.connection, :scheduled_messages, [])

    if scheduled_messages != current_messages do
      state
      |> synchronize_scheduled_message_timers(scheduled_messages)
      |> log_scheduled_message_sync(scheduled_messages)
      |> then(&{:reply, :ok, &1})
    else
      log_no_scheduled_message_changes(state.client_id)
      {:reply, :ok, state}
    end
  end

  def handle_call(_msg, _from, %{mqtt_client_pid: nil} = state) do
    {:reply, {:error, :not_connected}, state}
  end

  def handle_call({:subscribe, topic, opts}, _from, state) do
    state |> perform_topic_subscription(topic, opts) |> format_call_reply()
  end

  def handle_call({:unsubscribe, topic}, _from, state) do
    state |> perform_topic_unsubscription(topic) |> format_call_reply()
  end

  def handle_call({:publish, topic, payload, opts}, _from, state) do
    state |> perform_message_publication(topic, payload, opts) |> format_call_reply()
  end

  @impl true
  def handle_cast(:disconnect, %{mqtt_client_pid: nil} = state), do: {:noreply, state}

  def handle_cast(:disconnect, state) do
    state |> execute_client_disconnection() |> then(&{:noreply, &1})
  end

  @impl true
  def handle_info(:connect, state) do
    Logger.debug("Connecting to MQTT broker for client #{inspect(state)}")
    state |> attempt_mqtt_connection() |> handle_connection_result()
  end

  def handle_info({:DOWN, _ref, :process, pid, reason}, %{mqtt_client_pid: pid} = state) do
    Logger.warning("MQTT client #{state.client_id} went down: #{inspect(reason)}")
    state |> handle_client_process_termination(5_000) |> then(&{:noreply, &1})
  end

  def handle_info({:DOWN, _ref, :process, _other_pid, _reason}, state), do: {:noreply, state}

  def handle_info({:EXIT, pid, reason}, %{mqtt_client_pid: pid} = state) do
    Logger.warning("MQTT client #{state.client_id} exited: #{inspect(reason)}")
    state |> handle_client_process_termination(5_000) |> then(&{:noreply, &1})
  end

  def handle_info({:EXIT, _other_pid, _reason}, state), do: {:noreply, state}

  def handle_info({:send_scheduled_message, index, scheduled_message}, state) do
    state
    |> execute_scheduled_message(index, scheduled_message)
    |> reschedule_message_if_timer_exists(index, scheduled_message)
    |> then(&{:noreply, &1})
  end

  def handle_info({:publish, _}, state), do: {:noreply, state}

  def handle_info(msg, state) do
    Logger.info("Received unexpected message in MqttClient.Worker: #{inspect(msg)}")
    {:noreply, state}
  end

  @impl true
  def terminate(_reason, state) do
    state
    |> cleanup_all_timers()
    |> disconnect_client_safely()
    |> cleanup_client_registry()
    |> broadcast_final_disconnection()

    :ok
  end

  # State Management

  defp build_initial_state({broker, connection}) do
    %{
      client_id: connection.client_id,
      connection: connection,
      broker: broker,
      mqtt_client_pid: nil,
      mqtt_opts: nil,
      status: @status_disconnected,
      reconnect_timer: nil,
      scheduled_message_timers: %{},
      connection_time: nil
    }
  end

  defp build_debug_info(state) do
    %{
      client_id: state.client_id,
      broker_name: state.broker.name,
      status: state.status,
      scheduled_messages_count: length(Map.get(state.connection, :scheduled_messages, [])),
      scheduled_messages: Map.get(state.connection, :scheduled_messages, []),
      active_timers_count: map_size(state.scheduled_message_timers),
      active_timers: state.scheduled_message_timers,
      mqtt_client_pid: state.mqtt_client_pid
    }
  end

  # Connection Management

  defp attempt_mqtt_connection(state) do
    case establish_mqtt_client(state.connection, state.broker) do
      {:ok, client_pid, mqtt_opts} -> {:ok, client_pid, mqtt_opts, state}
      {:error, reason, error_message} -> {:error, reason, error_message, state}
    end
  end

  defp establish_mqtt_client(connection, broker) do
    connection
    |> ConnectionBuilder.build_mqtt_options(broker)
    |> tap(&Logger.debug("mqtt_opts: #{inspect(&1)}"))
    |> start_mqtt_client()
    |> case do
      {:ok, client_pid, mqtt_opts} -> connect_to_mqtt_broker(client_pid, broker, mqtt_opts)
      error -> error
    end
  end

  defp start_mqtt_client(mqtt_opts) do
    case :mqtt_client.start_link(mqtt_opts) do
      {:ok, client_pid} ->
        {:ok, client_pid, mqtt_opts}

      {:error, reason} ->
        reason |> ErrorHandler.format_mqtt_error() |> then(&{:error, reason, &1})
    end
  end

  defp connect_to_mqtt_broker(client_pid, broker, mqtt_opts) do
    try do
      broker
      |> determine_connection_method()
      |> execute_connection_method(client_pid)
      |> handle_connection_response(client_pid, broker, mqtt_opts)
    catch
      :exit, {:shutdown, :tcp_closed} ->
        handle_connection_exit(client_pid, :tcp_closed)

      :exit, {:socket_closed_before_connack, reason} ->
        handle_connection_exit(client_pid, {:socket_closed_before_connack, reason})

      :exit, reason ->
        handle_connection_exit(client_pid, reason)
    end
  end

  defp determine_connection_method(%{protocol: protocol}) when protocol in ["ws", "wss"],
    do: :ws_connect

  defp determine_connection_method(_broker), do: :connect

  defp execute_connection_method(:ws_connect, client_pid), do: :mqtt_client.ws_connect(client_pid)
  defp execute_connection_method(:connect, client_pid), do: :mqtt_client.connect(client_pid)

  defp handle_connection_response({:ok, _props}, client_pid, broker, mqtt_opts) do
    Logger.info("Connected to MQTT broker: #{broker.host}:#{broker.port} via #{broker.protocol}")
    {:ok, client_pid, mqtt_opts}
  end

  defp handle_connection_response({:error, reason}, client_pid, _broker, _mqtt_opts) do
    reason
    |> ErrorHandler.format_mqtt_error()
    |> tap(&Logger.error("Failed to connect to MQTT broker: #{inspect(reason)} - #{&1}"))
    |> then(fn error_message ->
      safe_stop_client(client_pid)
      {:error, reason, error_message}
    end)
  end

  defp handle_connection_exit(client_pid, reason) do
    reason
    |> ErrorHandler.extract_concise_error_message()
    |> tap(&Logger.error("Failed to connect to MQTT broker: #{&1}"))
    |> then(fn error_message ->
      safe_stop_client(client_pid)
      {:error, reason, error_message}
    end)
  end

  defp handle_connection_result({:ok, client_pid, mqtt_opts, state}) do
    Process.monitor(client_pid)

    state
    |> update_state_on_connection(client_pid, mqtt_opts)
    |> register_client_as_connected(client_pid, mqtt_opts)
    |> broadcast_connection_success()
    |> resubscribe_to_saved_topics()
    |> restart_saved_scheduled_messages()
    |> then(&{:noreply, &1})
  end

  defp handle_connection_result({:error, reason, error_message, state}) do
    Logger.error(
      "Failed to connect MQTT client #{state.client_id}: #{inspect(reason)} - #{error_message}"
    )

    Mqttable.MqttPacketProcessor.create_connection_error_trace(
      state.broker.name,
      state.client_id,
      reason,
      error_message
    )

    state
    |> register_client_as_reconnecting()
    |> broadcast_reconnection_status()
    |> schedule_reconnection(10_000)
    |> update_state_as_reconnecting()
    |> then(&{:noreply, &1})
  end

  # Topic Operations

  defp perform_topic_subscription(state, topic, opts) do
    with {:ok, sub_opts, props} <- prepare_subscription_options(opts),
         :ok <- Subscription.validate_topic_filter(topic),
         {:ok, result} <-
           execute_topic_subscription(state.mqtt_client_pid, topic, sub_opts, props) do
      broadcast_subscription_success(state, topic, sub_opts, Keyword.get(opts, :id))
      {:ok, result, state}
    else
      {:error, reason, error_message} ->
        Logger.error(
          "Failed to subscribe to topic #{topic}: #{inspect(reason)} - #{error_message}"
        )

        {:error, reason, error_message, state}
    end
  end

  defp prepare_subscription_options(opts) do
    sub_opts =
      opts
      |> Keyword.get(:sub_opts, [{:qos, 0}])
      |> Subscription.to_subscription_options()

    props =
      opts
      |> Keyword.get(:id)
      |> Subscription.prepare_subscription_properties()

    Logger.debug("Subscription properties: #{inspect(props)}")

    {:ok, sub_opts, props}
  end

  defp execute_topic_subscription(client_pid, topic, sub_opts, props) do
    try do
      case :mqtt_client.subscribe(client_pid, props, [{topic, sub_opts}]) do
        {:ok, props, reason_codes} -> check_subscription_result(reason_codes, props)
        {:error, reason} -> {:error, reason, ErrorHandler.format_mqtt_error(reason)}
      end
    catch
      :exit, reason -> {:error, reason, "Subscription failed: #{inspect(reason)}"}
    end
  end

  defp check_subscription_result(reason_codes, props) do
    case Subscription.check_subscription_success(reason_codes) do
      :ok ->
        {:ok, {props, reason_codes}}

      {:error, error_message} ->
        {:error, :subscription_rejected, error_message}
    end
  end

  defp perform_topic_unsubscription(state, topic) do
    try do
      case :mqtt_client.unsubscribe(state.mqtt_client_pid, %{}, [topic]) do
        {:ok, props, reason_codes} ->
          broadcast_unsubscription_success(state, topic)
          {:ok, {props, reason_codes}, state}

        {:error, reason} ->
          reason
          |> ErrorHandler.format_mqtt_error()
          |> tap(
            &Logger.error("Failed to unsubscribe from topic #{topic}: #{inspect(reason)} - #{&1}")
          )
          |> then(&{:error, reason, &1, state})
      end
    catch
      :exit, reason ->
        error_message = "Unsubscription failed: #{inspect(reason)}"
        Logger.error(error_message)
        {:error, reason, error_message, state}
    end
  end

  defp perform_message_publication(state, topic, payload, opts) do
    publish_options = [qos: Keyword.get(opts, :qos, 0), retain: Keyword.get(opts, :retain, false)]

    try do
      :mqtt_client.publish(
        state.mqtt_client_pid,
        topic,
        Keyword.get(opts, :properties, %{}),
        payload,
        publish_options
      )
      |> handle_publish_result(topic)
      |> then(&{:ok, &1, state})
    catch
      :exit, reason ->
        error_message = "Publish failed: #{inspect(reason)}"
        Logger.error(error_message)
        {:error, reason, error_message, state}
    end
  end

  defp handle_publish_result({:ok, packet_id}, topic) when is_map(packet_id) do
    packet_id |> Map.get(:reason_code, 0) |> log_publish_result(topic, packet_id)
    packet_id
  end

  defp handle_publish_result(:ok, topic) do
    Logger.debug("Published QoS 0 message to topic #{topic}")
    0
  end

  defp handle_publish_result({:error, reason}, topic) do
    reason
    |> ErrorHandler.format_mqtt_error()
    |> tap(&Logger.error("Failed to publish to topic #{topic}: #{inspect(reason)} - #{&1}"))
    |> then(&{:error, reason, &1})
  end

  defp log_publish_result(0, topic, packet_id) do
    Logger.debug(
      "Published message to topic #{inspect(topic)} with packet_id #{inspect(packet_id)}"
    )
  end

  defp log_publish_result(reason_code, topic, packet_id) do
    reason_name = Map.get(packet_id, :reason_code_name, "unknown")

    Logger.error(
      "Published message failed: #{reason_name} (code: #{reason_code}) for topic #{topic}"
    )
  end

  defp format_call_reply({:ok, result, state}), do: {:reply, {:ok, result}, state}

  defp format_call_reply({:error, reason, error_message, state}),
    do: {:reply, {:error, reason, error_message}, state}

  # Client Management

  defp execute_client_disconnection(state) do
    state
    |> cancel_all_scheduled_message_timers()
    |> disconnect_client_safely()
    |> register_client_as_disconnected()
    |> broadcast_disconnection_status()
    |> update_state_as_disconnected()
  end

  defp handle_client_process_termination(state, delay) do
    state
    |> cancel_all_scheduled_message_timers()
    |> register_client_as_reconnecting()
    |> broadcast_reconnection_status()
    |> schedule_reconnection(delay)
    |> update_state_as_reconnecting()
  end

  defp disconnect_client_safely(%{mqtt_client_pid: nil} = state) do
    state
  end

  defp disconnect_client_safely(%{mqtt_client_pid: client_pid} = state) do
    safe_disconnect(client_pid)
    safe_stop_client(client_pid)
    %{state | mqtt_client_pid: nil}
  end

  defp safe_disconnect(client_pid) do
    try do
      :mqtt_client.disconnect(client_pid)
    catch
      :exit, _ -> :ok
    end
  end

  defp safe_stop_client(client_pid) do
    try do
      :mqtt_client.stop(client_pid)
    rescue
      _error -> :ok
    catch
      :exit, {:noproc, _} ->
        :ok

      :exit, reason ->
        Logger.debug("Exit while stopping MQTT client #{inspect(client_pid)}: #{inspect(reason)}")

        :ok
    end
  end

  # State Updates

  defp update_state_on_connection(state, client_pid, mqtt_opts) do
    connection_time = DateTime.utc_now()

    %{
      state
      | mqtt_client_pid: client_pid,
        mqtt_opts: mqtt_opts,
        status: @status_connected,
        connection_time: connection_time
    }
  end

  defp update_state_as_reconnecting(state) do
    %{
      state
      | mqtt_client_pid: nil,
        status: @status_reconnecting,
        scheduled_message_timers: %{},
        connection_time: nil
    }
  end

  defp update_state_as_disconnected(state) do
    %{
      state
      | mqtt_client_pid: nil,
        status: @status_disconnected,
        scheduled_message_timers: %{},
        connection_time: nil
    }
  end

  # Client Registry Management

  defp register_client_as_connecting(state) do
    ClientRegistry.register_client(
      state.broker.name,
      state.client_id,
      self(),
      nil,
      nil,
      @status_connecting
    )

    state
  end

  defp register_client_as_connected(state, client_pid, mqtt_opts) do
    ClientRegistry.register_client(
      state.broker.name,
      state.client_id,
      self(),
      client_pid,
      mqtt_opts,
      @status_connected
    )

    state
  end

  defp register_client_as_reconnecting(state) do
    ClientRegistry.register_client(
      state.broker.name,
      state.client_id,
      self(),
      nil,
      nil,
      @status_reconnecting
    )

    state
  end

  defp register_client_as_disconnected(state) do
    ClientRegistry.register_client(
      state.broker.name,
      state.client_id,
      self(),
      nil,
      nil,
      @status_disconnected
    )

    state
  end

  # Scheduled Messages

  defp execute_scheduled_message(%{mqtt_client_pid: nil} = state, index, _scheduled_message) do
    Logger.debug(
      "Skipping scheduled message #{index} for client #{state.client_id}: not connected"
    )

    state
  end

  defp execute_scheduled_message(state, index, scheduled_message) do
    topic = scheduled_message.topic
    payload = get_scheduled_message_payload(scheduled_message, state.broker)
    qos = Map.get(scheduled_message, :qos, 0)
    retain = Map.get(scheduled_message, :retain, false)
    properties = build_scheduled_message_properties(scheduled_message, state.connection)

    try do
      :mqtt_client.publish(state.mqtt_client_pid, topic, properties, payload,
        qos: qos,
        retain: retain
      )

      Logger.debug("Scheduled(#{index}) message published successfully to topic #{topic}")
    rescue
      error ->
        Logger.error("Error publishing scheduled(#{index}) message: #{inspect(error)}")
    catch
      :exit, reason ->
        Logger.error("Failed to publish scheduled(#{index}) message: #{inspect(reason)}")
    end

    state
  end

  defp reschedule_message_if_timer_exists(state, index, scheduled_message) do
    case Map.get(state.scheduled_message_timers, index) do
      nil ->
        state

      old_timer_ref ->
        Process.cancel_timer(old_timer_ref)

        new_timer_ref =
          Process.send_after(
            self(),
            {:send_scheduled_message, index, scheduled_message},
            scheduled_message.interval_ms
          )

        %{
          state
          | scheduled_message_timers:
              Map.put(state.scheduled_message_timers, index, new_timer_ref)
        }
    end
  end

  defp synchronize_scheduled_message_timers(state, scheduled_messages) do
    state
    |> cancel_all_scheduled_message_timers()
    |> create_scheduled_message_timers(scheduled_messages)
    |> update_connection_scheduled_messages(scheduled_messages)
  end

  defp create_scheduled_message_timers(state, scheduled_messages) do
    updated_timers =
      scheduled_messages
      |> Enum.with_index()
      |> Enum.reduce(%{}, fn {scheduled_message, index}, acc ->
        timer_ref =
          Process.send_after(
            self(),
            {:send_scheduled_message, index, scheduled_message},
            scheduled_message.interval_ms
          )

        Map.put(acc, index, timer_ref)
      end)

    %{state | scheduled_message_timers: updated_timers}
  end

  defp update_connection_scheduled_messages(state, scheduled_messages) do
    %{state | connection: Map.put(state.connection, :scheduled_messages, scheduled_messages)}
  end

  defp log_scheduled_message_sync(state, scheduled_messages) do
    Logger.info(
      "Synchronized #{length(scheduled_messages)} scheduled messages for client #{state.client_id} (changes detected)"
    )

    state
  end

  defp log_no_scheduled_message_changes(client_id) do
    Logger.debug(
      "No changes in scheduled messages for client #{client_id}, keeping existing timers"
    )
  end

  defp cancel_all_scheduled_message_timers(state) do
    Enum.each(state.scheduled_message_timers, fn {_index, timer_ref} ->
      Process.cancel_timer(timer_ref)
    end)

    %{state | scheduled_message_timers: %{}}
  end

  defp restart_saved_scheduled_messages(state) do
    scheduled_messages = Map.get(state.connection, :scheduled_messages, [])

    if scheduled_messages != [] do
      Logger.info(
        "Starting #{length(scheduled_messages)} scheduled messages for client #{state.client_id}"
      )

      create_scheduled_message_timers(state, scheduled_messages)
    else
      state
    end
  end

  # Topic Resubscription

  defp resubscribe_to_saved_topics(state) do
    topics = Map.get(state.connection, :topics, [])

    if topics != [] do
      Logger.info("Resubscribing client #{state.client_id} to #{length(topics)} saved topics")

      topics
      |> Subscription.process_saved_topics_for_resubscription()
      |> Enum.each(&attempt_topic_resubscription(state, &1))
    end

    state
  end

  defp attempt_topic_resubscription(state, {topic, {sub_opts, props}}) do
    case Subscription.prepare_topic_for_subscription(topic, sub_opts, props) do
      {:ok, {validated_topic, processed_opts, validated_props}} ->
        execute_topic_resubscription(
          state,
          topic,
          validated_topic,
          processed_opts,
          validated_props
        )

      {:error, error_message} ->
        handle_resubscription_error(state, topic, sub_opts, error_message)
    end
  end

  defp execute_topic_resubscription(
         state,
         original_topic,
         validated_topic,
         processed_opts,
         validated_props
       ) do
    try do
      case :mqtt_client.subscribe(state.mqtt_client_pid, validated_props, [
             {validated_topic, processed_opts}
           ]) do
        {:ok, _props, reason_codes} ->
          handle_resubscription_result(state, original_topic, reason_codes, processed_opts)

        {:error, reason} ->
          handle_resubscription_error(
            state,
            original_topic,
            processed_opts,
            ErrorHandler.format_mqtt_error(reason)
          )
      end
    catch
      :exit, reason ->
        error_message =
          "Exit while resubscribing client #{state.client_id} to topic #{original_topic}: #{inspect(reason)}"

        handle_resubscription_error(state, original_topic, processed_opts, error_message)
    end
  end

  defp handle_resubscription_result(state, topic, reason_codes, opts) do
    case Subscription.check_subscription_success(reason_codes) do
      :ok -> Subscription.log_resubscription_attempt(state.client_id, topic, opts, :ok)
      {:error, error_message} -> handle_resubscription_error(state, topic, opts, error_message)
    end
  end

  defp handle_resubscription_error(state, topic, opts, error_message) do
    Logger.error(
      "Failed to resubscribe client #{state.client_id} to topic #{topic} with opts #{inspect(opts)}: #{error_message}"
    )
  end

  # Timer Management

  defp schedule_reconnection(state, delay) do
    cancel_existing_reconnect_timer(state)
    timer_ref = Process.send_after(self(), :connect, delay)
    %{state | reconnect_timer: timer_ref}
  end

  defp cancel_existing_reconnect_timer(%{reconnect_timer: timer_ref})
       when is_reference(timer_ref) do
    Process.cancel_timer(timer_ref)
  end

  defp cancel_existing_reconnect_timer(_state), do: :ok

  # Cleanup

  defp cleanup_all_timers(state) do
    cancel_existing_reconnect_timer(state)
    cancel_all_scheduled_message_timers(state)
  end

  defp cleanup_client_registry(state) do
    ClientRegistry.unregister_client(state.broker.name, state.client_id)
    state
  end

  # Broadcasting

  defp broadcast_connection_success(state) do
    broadcast_status_change(state.broker.name, state.client_id, @status_connected)

    # Broadcast connection time using the time stored in state
    if state.connection_time do
      PubSub.broadcast(
        Mqttable.PubSub,
        @pubsub_topic,
        {:mqtt_client_connection_time, state.broker.name, state.client_id, state.connection_time}
      )
    end

    state
  end

  defp broadcast_reconnection_status(state) do
    broadcast_status_change(state.broker.name, state.client_id, @status_reconnecting)
    state
  end

  defp broadcast_disconnection_status(state) do
    broadcast_status_change(state.broker.name, state.client_id, @status_disconnected)
    state
  end

  defp broadcast_final_disconnection(state) do
    broadcast_status_change(state.broker.name, state.client_id, @status_disconnected)
    state
  end

  defp broadcast_status_change(broker_name, client_id, status) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_status_changed, broker_name, client_id, status}
    )
  end

  defp broadcast_subscription_success(state, topic, opts, sub_id) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_topic_subscribed, state.broker.name, state.client_id, topic, opts, sub_id}
    )
  end

  defp broadcast_unsubscription_success(state, topic) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_topic_unsubscribed, state.broker.name, state.client_id, topic}
    )
  end

  # Scheduled Message Payload Processing

  defp get_scheduled_message_payload(scheduled_message, broker) do
    payload_format = scheduled_message.payload_format
    payload = get_payload_from_scheduled_message(scheduled_message, payload_format)
    file_encoding = scheduled_message.file_encoding

    # Handle template rendering if needed
    processed_payload =
      if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
        variables = get_broker_variables_from_broker(broker)

        case Mqttable.Templating.Engine.render(payload, %{}, variables) do
          {:ok, rendered_payload} ->
            rendered_payload

          {:error, reason} ->
            Logger.warning(
              "Template rendering failed for scheduled message: #{reason}, using original payload"
            )

            payload
        end
      else
        payload
      end

    # Handle payload encoding based on format
    case payload_format do
      "file" ->
        case Mqttable.Uploads.FileStorage.read_file(processed_payload) do
          {:ok, file_content} ->
            encode_file_content(file_content, file_encoding)

          {:error, error_message} ->
            Logger.warning(
              "Failed to read file for scheduled message: #{error_message}, using empty payload"
            )

            ""
        end

      "hex" ->
        cleaned = String.replace(processed_payload, ~r/\s/, "")

        case Base.decode16(cleaned, case: :mixed) do
          {:ok, binary} ->
            binary

          :error ->
            Logger.warning(
              "Failed to decode hex payload for scheduled message, using original payload"
            )

            processed_payload
        end

      _ ->
        processed_payload
    end
  end

  defp get_payload_from_scheduled_message(scheduled_message, "text"),
    do: scheduled_message.payload_text

  defp get_payload_from_scheduled_message(scheduled_message, "json"),
    do: scheduled_message.payload_json

  defp get_payload_from_scheduled_message(scheduled_message, "hex"),
    do: scheduled_message.payload_hex

  defp get_payload_from_scheduled_message(scheduled_message, "file"),
    do: scheduled_message.payload_file

  defp encode_file_content(file_content, "base64"), do: Base.encode64(file_content)
  defp encode_file_content(file_content, _), do: file_content

  defp get_broker_variables_from_broker(broker) when is_map(broker) do
    broker
    |> Map.get(:variables, [])
    |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
    |> Enum.reduce(%{}, fn var, acc ->
      name = Map.get(var, :name)
      value = Map.get(var, :value, "")
      if name && name != "", do: Map.put(acc, name, value), else: acc
    end)
  end

  defp get_broker_variables_from_broker(_broker), do: %{}

  # MQTT 5.0 Properties for Scheduled Messages

  defp build_scheduled_message_properties(scheduled_message, connection)
       when is_map(scheduled_message) do
    case Map.get(connection, :mqtt_version) do
      "5.0" ->
        properties =
          %{}
          |> add_content_type_property(scheduled_message)
          |> add_payload_format_indicator_property(scheduled_message)
          |> add_message_expiry_interval_property(scheduled_message)
          |> add_topic_alias_property(scheduled_message)
          |> add_response_topic_property(scheduled_message)
          |> add_correlation_data_property(scheduled_message)
          |> add_user_properties_to_scheduled_message(scheduled_message)

        properties

      _ ->
        %{}
    end
  end

  defp build_scheduled_message_properties(_, _), do: %{}

  defp add_content_type_property(properties, scheduled_message) do
    case get_scheduled_msg_property(scheduled_message, :content_type, "") do
      "" -> properties
      content_type -> Map.put(properties, :"Content-Type", content_type)
    end
  end

  defp add_payload_format_indicator_property(properties, scheduled_message) do
    if get_scheduled_msg_property(scheduled_message, :payload_format_indicator, false) do
      Map.put(properties, :"Payload-Format-Indicator", 1)
    else
      properties
    end
  end

  defp add_message_expiry_interval_property(properties, scheduled_message) do
    case get_scheduled_msg_property(scheduled_message, :message_expiry_interval, 0) do
      0 -> properties
      expiry -> Map.put(properties, :"Message-Expiry-Interval", expiry)
    end
  end

  defp add_topic_alias_property(properties, scheduled_message) do
    case get_scheduled_msg_property(scheduled_message, :topic_alias, 0) do
      0 -> properties
      alias -> Map.put(properties, :"Topic-Alias", alias)
    end
  end

  defp add_response_topic_property(properties, scheduled_message) do
    case get_scheduled_msg_property(scheduled_message, :response_topic, "") do
      "" -> properties
      response_topic -> Map.put(properties, :"Response-Topic", response_topic)
    end
  end

  defp add_correlation_data_property(properties, scheduled_message) do
    case get_scheduled_msg_property(scheduled_message, :correlation_data, "") do
      "" -> properties
      correlation_data -> Map.put(properties, :"Correlation-Data", correlation_data)
    end
  end

  defp add_user_properties_to_scheduled_message(properties, scheduled_message) do
    user_properties = get_scheduled_msg_property(scheduled_message, :user_properties, [])

    valid_user_properties =
      Enum.filter(user_properties, fn
        %{"key" => key, "value" => value} when is_binary(key) and is_binary(value) ->
          key != "" && value != ""

        _ ->
          false
      end)

    if length(valid_user_properties) > 0 do
      user_props_list =
        Enum.map(valid_user_properties, fn %{"key" => key, "value" => value} -> {key, value} end)

      Map.put(properties, :"User-Property", user_props_list)
    else
      properties
    end
  end

  defp get_scheduled_msg_property(scheduled_msg, property, default) when is_map(scheduled_msg) do
    Map.get(scheduled_msg, property, Map.get(scheduled_msg, to_string(property), default))
  end

  defp get_scheduled_msg_property(_, _, default), do: default
end
