defmodule MqttableWeb.ProxyLive do
  @moduledoc """
  LiveView for managing MQTT Proxies.

  Provides a dedicated interface for creating, configuring, and monitoring
  MQTT proxies with toxics (latency, jitter, loss, bandwidth throttling).
  """
  use MqttableWeb, :live_view

  alias Mqttable.MqttProxy.Manager
  alias Phoenix.PubSub

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      PubSub.subscribe(Mqttable.PubSub, "mqtt_trace")
    end

    socket =
      socket
      |> assign(:page_title, "MQTT Proxies")
      |> assign(:proxies, Manager.list())
      |> assign(:selected_proxy, nil)
      |> assign(:trace_messages, [])

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    selected_proxy = Map.get(params, "proxy")

    socket =
      socket
      |> assign(:selected_proxy, selected_proxy)
      |> maybe_load_trace_messages(selected_proxy)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:mqtt_trace_message, message}, socket) do
    # Only show trace messages for the selected proxy
    if socket.assigns.selected_proxy && message.broker_name == socket.assigns.selected_proxy do
      trace_messages = [message | socket.assigns.trace_messages] |> Enum.take(1000)
      {:noreply, assign(socket, :trace_messages, trace_messages)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:proxy_updated}, socket) do
    {:noreply, assign(socket, :proxies, Manager.list())}
  end

  @impl true
  def handle_info({:select_proxy, proxy_name}, socket) do
    {:noreply, push_patch(socket, to: ~p"/proxy?proxy=#{proxy_name}")}
  end

  @impl true
  def handle_event("select_proxy", %{"proxy" => proxy_name}, socket) do
    {:noreply, push_patch(socket, to: ~p"/proxy?proxy=#{proxy_name}")}
  end

  @impl true
  def handle_event("clear_trace", _params, socket) do
    {:noreply, assign(socket, :trace_messages, [])}
  end

  @impl true
  def handle_event("open_settings_modal", _params, socket) do
    # Handle settings modal - can be implemented later
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="h-full flex flex-col bg-base-100">
      <!-- Header -->
      <div class="border-b border-base-300 p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <span class="hero-forward size-6 text-primary"></span>
            <h1 class="text-2xl font-bold">MQTT Proxies</h1>
          </div>
          <div class="text-sm text-base-content/70">
            Network simulation and monitoring
          </div>
        </div>
      </div>
      
    <!-- Main Content -->
      <div class="flex-1 flex overflow-hidden">
        <!-- Left Panel: Proxy Management -->
        <div class="w-1/2 border-r border-base-300 flex flex-col">
          <div class="p-4 border-b border-base-300">
            <h2 class="text-lg font-semibold mb-4">Proxy Management</h2>
            <.live_component module={MqttableWeb.ProxyManagerComponent} id="proxy-manager" />
          </div>
          
    <!-- Proxy List -->
          <div class="flex-1 overflow-y-auto p-4">
            <h3 class="text-md font-medium mb-3">Active Proxies</h3>
            <div class="space-y-2">
              <%= for proxy <- @proxies do %>
                <div
                  class={[
                    "card bg-base-200 cursor-pointer transition-all duration-200",
                    if(@selected_proxy == proxy.name,
                      do: "ring-2 ring-primary",
                      else: "hover:bg-base-300"
                    )
                  ]}
                  phx-click="select_proxy"
                  phx-value-proxy={proxy.name}
                >
                  <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="font-medium">{proxy.name}</h4>
                        <p class="text-sm text-base-content/70">
                          {proxy.listen_host}:{proxy.listen_port} → {proxy.upstream_host}:{proxy.upstream_port}
                        </p>
                      </div>
                      <div class="flex items-center space-x-2">
                        <div class={[
                          "badge badge-sm",
                          if(proxy.enabled, do: "badge-success", else: "badge-error")
                        ]}>
                          {if proxy.enabled, do: "Enabled", else: "Disabled"}
                        </div>
                        <%= if Manager.running?(proxy.name) do %>
                          <div class="badge badge-sm badge-primary">Running</div>
                        <% end %>
                      </div>
                    </div>
                    
    <!-- Toxics Summary -->
                    <%= if proxy.toxics[:enabled] do %>
                      <div class="mt-2 text-xs text-base-content/60">
                        Toxics: {if proxy.toxics[:latency_ms] > 0,
                          do: "#{proxy.toxics[:latency_ms]}ms latency "}
                        {if proxy.toxics[:jitter_ms] > 0, do: "±#{proxy.toxics[:jitter_ms]}ms jitter "}
                        {if proxy.toxics[:loss_percent] > 0,
                          do: "#{proxy.toxics[:loss_percent]}% loss "}
                        {if proxy.toxics[:bandwidth_kbps] > 0,
                          do: "#{proxy.toxics[:bandwidth_kbps]}kbps limit"}
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>

              <%= if @proxies == [] do %>
                <div class="text-center py-8 text-base-content/50">
                  <span class="hero-forward size-12 mx-auto block mb-2"></span>
                  <p>No proxies configured</p>
                  <p class="text-sm">Create your first proxy above</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
        
    <!-- Right Panel: Trace Monitoring -->
        <div class="w-1/2 flex flex-col">
          <div class="p-4 border-b border-base-300">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold">
                <%= if @selected_proxy do %>
                  Trace: {@selected_proxy}
                <% else %>
                  Packet Trace
                <% end %>
              </h2>
              <%= if @selected_proxy do %>
                <button class="btn btn-sm btn-ghost" phx-click="clear_trace">
                  <span class="hero-trash size-4"></span> Clear
                </button>
              <% end %>
            </div>
          </div>

          <div class="flex-1 overflow-hidden">
            <%= if @selected_proxy do %>
              <.live_component
                module={MqttableWeb.TraceGridComponent}
                id="proxy-trace-grid"
                broker_name={@selected_proxy}
                trace_messages={@trace_messages}
              />
            <% else %>
              <div class="flex items-center justify-center h-full text-base-content/50">
                <div class="text-center">
                  <span class="hero-eye size-12 mx-auto block mb-2"></span>
                  <p>Select a proxy to view packet trace</p>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp maybe_load_trace_messages(socket, nil), do: assign(socket, :trace_messages, [])

  defp maybe_load_trace_messages(socket, proxy_name) do
    # Load recent trace messages for this proxy from TraceManager
    messages = Mqttable.TraceManager.get_messages(proxy_name) |> Enum.take(1000)
    assign(socket, :trace_messages, messages)
  end
end
