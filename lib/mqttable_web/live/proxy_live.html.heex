<div
  id="proxy-page"
  class="h-screen flex bg-base-100"
  phx-hook="GlobalKeyboardShortcuts"
>
  <!-- Copy to Clipboard Hook Container -->
  <div id="copy-to-clipboard-hook" phx-hook="CopyToClipboard" style="display: none;"></div>

  <!-- Icon Menu -->
  <.icon_menu class="panel-icon-menu">
    <:top_item icon="hero-inbox-stack" tooltip="Brokers" navigate={~p"/"} />
    <:top_item icon="hero-forward" tooltip="Proxy" active={@live_action == :index} navigate={~p"/proxy"} />
    <:top_item icon="hero-wrench-screwdriver" href="#toolbox" tooltip="Toolbox" />

    <:bottom_item icon="hero-cog-6-tooth" phx_click="open_settings_modal" tooltip="Settings" />
    <:bottom_item icon="hero-user-circle" tooltip="Account" />
  </.icon_menu>

  <!-- Main Content Area -->
  <div class="flex-1 flex flex-col overflow-y-auto">
    <!-- Header -->
    <div class="border-b border-base-300 p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <span class="hero-forward size-6 text-primary"></span>
          <h1 class="text-2xl font-bold">MQTT Proxies</h1>
        </div>
        <div class="text-sm text-base-content/70">
          Network simulation and monitoring
        </div>
      </div>
    </div>

    <!-- Proxy Management Form -->
    <div class="border-b border-base-300 p-4">
      <h2 class="text-lg font-semibold mb-4">Create New Proxy</h2>
      <.live_component module={MqttableWeb.ProxyManagerComponent} id="proxy-manager" />
    </div>

    <!-- Proxy Table -->
    <div class="border-b border-base-300 p-4">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold">Proxy Management</h2>
        <div class="text-sm text-base-content/70">
          <%= length(@proxies) %> proxy(es) configured
        </div>
      </div>
      <.live_component
        module={MqttableWeb.ProxyTableComponent}
        id="proxy-table"
        proxies={@proxies}
        selected_proxy={@selected_proxy}
      />
    </div>

    <!-- Toxics Configuration for Selected Proxy -->
    <%= if @selected_proxy do %>
      <div class="border-b border-base-300 p-4">
        <%= case Enum.find(@proxies, &(&1.name == @selected_proxy)) do %>
          <% nil -> %>
            <div class="text-center text-base-content/50">
              <p>Selected proxy not found</p>
            </div>
          <% proxy -> %>
            <h2 class="text-lg font-semibold mb-4">Toxics Configuration: <%= proxy.name %></h2>
            <.live_component
              module={MqttableWeb.ProxyToxicsComponent}
              id="proxy-toxics"
              proxy={proxy}
            />
        <% end %>
      </div>
    <% end %>

    <!-- Trace Monitoring -->
    <div class="flex-1 flex flex-col">
      <div class="p-4 border-b border-base-300">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">
            <%= if @selected_proxy do %>
              Packet Trace: <%= @selected_proxy %>
            <% else %>
              Packet Trace
            <% end %>
          </h2>
          <%= if @selected_proxy do %>
            <button class="btn btn-sm btn-ghost" phx-click="clear_trace">
              <span class="hero-trash size-4"></span>
              Clear Trace
            </button>
          <% end %>
        </div>
      </div>

      <div class="flex-1 overflow-hidden">
        <%= if @selected_proxy do %>
          <.live_component
            module={MqttableWeb.TraceGridComponent}
            id="proxy-trace-grid"
            broker_name={@selected_proxy}
          />
        <% else %>
          <div class="flex items-center justify-center h-full text-base-content/50">
            <div class="text-center">
              <span class="hero-eye size-12 mx-auto block mb-2"></span>
              <p class="text-lg">Select a proxy to view packet trace</p>
              <p class="text-sm">Click on any proxy in the table above to start monitoring</p>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
