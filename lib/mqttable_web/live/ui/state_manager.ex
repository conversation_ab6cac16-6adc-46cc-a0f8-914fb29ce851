defmodule MqttableWeb.UI.StateManager do
  @moduledoc """
  Module for managing UI state.
  This module provides functions for managing UI state, such as expanded sets.
  """

  require Logger
  import Phoenix.Component
  import Phoenix.LiveView

  # Import verified routes
  use Phoenix.VerifiedRoutes,
    endpoint: MqttableWeb.Endpoint,
    router: MqttableWeb.Router,
    statics: MqttableWeb.static_paths()

  alias Mqttable.ConnectionSets
  alias MqttableWeb.Utils.ConnectionHelpers

  @doc """
  Handles the close_modal event.
  """
  def handle_close_modal(socket) do
    {:noreply,
     socket
     |> assign(:show_modal, false)
     |> assign(:modal_type, nil)
     |> assign(:edit_var, nil)
     |> assign(:edit_connection_set, nil)
     |> assign(:connection_set, nil)}
  end

  @doc """
  Handles the open_connections_modal event.
  """
  def handle_open_connections_modal(socket) do
    # Check if there are any connection sets
    if Enum.empty?(socket.assigns.connection_sets) do
      # If no connection sets exist, open the new connection set modal
      variables = [%{name: "", value: "", enabled: true}]

      new_set = %{
        protocol: "mqtt",
        host: "",
        port: "1883",
        color: "blue",
        variables: variables
      }

      socket =
        socket
        |> assign(:show_modal, true)
        |> assign(:modal_type, :new_connection_set)
        |> assign(:edit_connection_set, new_set)

      # Schedule focus on the host field after the modal is rendered
      # Process.send_after(self(), {:focus_element, "connection-set-host"}, 100)

      {:noreply, socket}
    else
      # If connection sets exist, show the first one
      first_set = List.first(socket.assigns.connection_sets)

      # Load initial trace messages for the first broker (first page only)
      trace_messages =
        case Mqttable.TraceManager.get_messages_paginated(first_set.name, 50, 0) do
          {:ok, messages, _has_more} -> messages
          _error -> []
        end

      # No longer need to broadcast active_set changes - it's now local state only

      {:noreply,
       socket
       |> assign(:active_connection_set, first_set)
       |> stream(:trace_messages, trace_messages)}
    end
  end

  @doc """
  Handles the connection_sets_updated message.
  """
  def handle_connection_sets_updated(socket, updated_connection_sets) do
    # Get the current active connection set name
    active_connection_set_name =
      if socket.assigns.active_connection_set,
        do: socket.assigns.active_connection_set.name,
        else: nil

    Logger.debug(
      "StateManager.handle_connection_sets_updated called with active_connection_set_name: #{inspect(active_connection_set_name)}"
    )

    # Only select a new active broker if we don't have one currently
    # This prevents overriding the broker set by URL parameters
    updated_active_connection_set =
      if active_connection_set_name do
        # We have an active broker, try to keep it if it still exists
        Logger.debug("Keeping existing active broker: #{active_connection_set_name}")

        ConnectionHelpers.find_connection_set_by_name(
          updated_connection_sets,
          active_connection_set_name
        ) ||
          socket.assigns.active_connection_set
      else
        # No active broker, don't auto-select one - let URL params or user action decide
        Logger.debug("No active broker, not auto-selecting")
        nil
      end

    # Since we no longer use broker menu, we don't need to manage broker expansion states
    # Only keep connection table expansion states
    expanded_sets = socket.assigns.expanded_sets

    # Save the updated expanded sets state
    ConnectionSets.update_expanded_sets(expanded_sets)

    # Only reload trace messages if the active broker has actually changed
    socket =
      if updated_active_connection_set &&
           (socket.assigns.active_connection_set == nil ||
              socket.assigns.active_connection_set.name != updated_active_connection_set.name) do
        # Active broker changed, reload trace messages
        trace_messages = Mqttable.TraceManager.get_messages(updated_active_connection_set.name)

        socket
        |> assign(:connection_sets, updated_connection_sets)
        |> assign(:expanded_sets, expanded_sets)
        |> assign(:active_connection_set, updated_active_connection_set)
        |> stream(:trace_messages, trace_messages)
      else
        # Active broker didn't change, just update other assigns
        socket
        |> assign(:connection_sets, updated_connection_sets)
        |> assign(:expanded_sets, expanded_sets)
        |> assign(:active_connection_set, updated_active_connection_set)
      end

    {:noreply, socket}
  end

  @doc """
  Handles the ui_state_updated message.
  """
  def handle_ui_state_updated(socket, updated_ui_state) do
    # Get the expanded sets from the updated UI state
    expanded_sets = Map.get(updated_ui_state, :expanded_sets, %{})

    # Since we no longer use broker menu, we don't need to manage broker expansion states
    # Only keep connection table expansion states
    updated_expanded_sets = expanded_sets

    socket = assign(socket, :expanded_sets, updated_expanded_sets)

    # active_connection_set is now purely local state, no longer synchronized via UI state
    {:noreply, socket}
  end

  @doc """
  Handles the focus_element message.
  """
  def handle_focus_element(socket, element_id) do
    {:noreply, push_event(socket, "focus_element", %{id: element_id})}
  end
end
