defmodule MqttableWeb.ProxyManagerComponent do
  @moduledoc """
  LiveComponent for managing native MQTT proxies and their toxics.

  - Lists proxies from Mqttable.MqttProxy.Manager
  - Create or update proxies (persisted in priv/data/proxies.json)
  - Enable/disable and adjust toxics at runtime (latency, jitter, loss, bandwidth)

  Uses DaisyUI components for UI controls.
  """
  use MqttableWeb, :live_component

  alias Mqttable.MqttProxy.Manager

  @impl true
  def update(assigns, socket) do
    proxies = Manager.list()

    socket =
      socket
      |> assign(assigns)
      |> assign_new(:proxies, fn -> proxies end)
      |> assign_new(:form, fn -> default_form() end)

    {:ok, socket}
  end

  @impl true
  def handle_event("create_proxy", params, socket) do
    proxy = params_to_proxy(params)

    case Manager.upsert(proxy) do
      {:ok, _} ->
        # Notify parent LiveView to refresh proxy list
        send(self(), {:proxy_updated})
        {:noreply, assign(socket, :proxies, Manager.list())}

      {:error, reason} ->
        {:noreply, assign(socket, :error, to_string(reason))}
    end
  end

  @impl true
  def handle_event("toggle_enable", %{"name" => name, "enabled" => enabled}, socket) do
    enabled? = enabled == "true" || enabled == true
    _ = Manager.enable(name, enabled?)
    send(self(), {:proxy_updated})
    {:noreply, assign(socket, :proxies, Manager.list())}
  end

  @impl true
  def handle_event("update_toxics", params, socket) do
    name = Map.get(params, "name")

    toxics = %{
      enabled: true,
      latency_ms: parse_int(params["latency_ms"], 0),
      jitter_ms: parse_int(params["jitter_ms"], 0),
      loss_percent: parse_int(params["loss_percent"], 0),
      bandwidth_kbps: parse_int(params["bandwidth_kbps"], 0)
    }

    _ = Manager.update_toxics(name, toxics)
    send(self(), {:proxy_updated})
    {:noreply, assign(socket, :proxies, Manager.list())}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- Create/Update Proxy Form -->

      <form phx-submit="create_proxy" phx-target={@myself} class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Proxy Name</span>
            </label>
            <input
              name="name"
              placeholder="e.g., mqtt-proxy-1"
              class="input input-bordered input-sm"
              required
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Listen Address</span>
            </label>
            <div class="flex gap-2">
              <input
                name="listen_host"
                placeholder="127.0.0.1"
                class="input input-bordered input-sm flex-1"
              />
              <input
                name="listen_port"
                placeholder="18883"
                class="input input-bordered input-sm w-20"
              />
            </div>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Upstream Address</span>
            </label>
            <div class="flex gap-2">
              <input
                name="upstream_host"
                placeholder="127.0.0.1"
                class="input input-bordered input-sm flex-1"
              />
              <input
                name="upstream_port"
                placeholder="1883"
                class="input input-bordered input-sm w-20"
              />
            </div>
          </div>

          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">Enable on creation</span>
              <input type="checkbox" name="enabled" class="toggle toggle-primary toggle-sm" />
            </label>
          </div>
        </div>

        <div class="flex justify-end">
          <button class="btn btn-primary btn-sm">
            <span class="hero-plus size-4"></span> Create Proxy
          </button>
        </div>
      </form>
    </div>
    """
  end

  # helpers

  defp default_form do
    %{
      "name" => "",
      "listen_host" => "127.0.0.1",
      "listen_port" => "18883",
      "upstream_host" => "127.0.0.1",
      "upstream_port" => "1883",
      "enabled" => false
    }
  end

  defp params_to_proxy(params) do
    %{
      name: Map.get(params, "name"),
      listen_host: Map.get(params, "listen_host", "127.0.0.1"),
      listen_port: parse_int(params["listen_port"], 0),
      upstream_host: Map.get(params, "upstream_host", "127.0.0.1"),
      upstream_port: parse_int(params["upstream_port"], 0),
      enabled: parse_bool(params["enabled"]),
      toxics: %{
        enabled: true,
        latency_ms: 0,
        jitter_ms: 0,
        loss_percent: 0,
        bandwidth_kbps: 0
      }
    }
  end

  defp parse_int(nil, default), do: default
  defp parse_int("", default), do: default

  defp parse_int(v, default) when is_binary(v) do
    case Integer.parse(v) do
      {i, _} -> i
      :error -> default
    end
  end

  defp parse_int(v, _default) when is_integer(v), do: v

  defp parse_bool(v) when v in ["on", "true", true], do: true
  defp parse_bool(_), do: false
end
