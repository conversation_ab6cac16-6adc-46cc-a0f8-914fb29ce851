defmodule MqttableWeb.ProxyToxicsComponent do
  @moduledoc """
  LiveComponent for managing proxy toxics (latency, jitter, loss, bandwidth).
  
  Provides a dedicated interface for adjusting network simulation parameters
  for a specific proxy.
  """
  use MqttableWeb, :live_component

  alias Mqttable.MqttProxy.Manager

  @impl true
  def update(%{proxy: proxy} = assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:toxics, proxy.toxics || %{})

    {:ok, socket}
  end

  @impl true
  def handle_event("update_toxics", params, socket) do
    proxy_name = socket.assigns.proxy.name

    toxics = %{
      enabled: parse_bool(params["enabled"]),
      latency_ms: parse_int(params["latency_ms"], 0),
      jitter_ms: parse_int(params["jitter_ms"], 0),
      loss_percent: parse_int(params["loss_percent"], 0),
      bandwidth_kbps: parse_int(params["bandwidth_kbps"], 0)
    }

    case Manager.update_toxics(proxy_name, toxics) do
      :ok ->
        send(self(), {:proxy_updated})
        {:noreply, assign(socket, :toxics, toxics)}

      {:error, _reason} ->
        {:noreply, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="card bg-base-200">
      <div class="card-body p-4">
        <h3 class="card-title text-lg">Network Toxics</h3>
        <p class="text-sm text-base-content/70 mb-4">
          Simulate network conditions for <%= @proxy.name %>
        </p>

        <form phx-submit="update_toxics" phx-target={@myself} class="space-y-4">
          <!-- Enable Toxics -->
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">Enable toxics</span>
              <input 
                type="checkbox" 
                name="enabled" 
                class="toggle toggle-primary" 
                checked={@toxics[:enabled] || false}
              />
            </label>
          </div>

          <!-- Latency -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Latency (ms)</span>
              <span class="label-text-alt">Base delay for all packets</span>
            </label>
            <input 
              type="number" 
              name="latency_ms" 
              value={@toxics[:latency_ms] || 0}
              min="0" 
              max="10000"
              class="input input-bordered input-sm"
              placeholder="0"
            />
          </div>

          <!-- Jitter -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Jitter (ms)</span>
              <span class="label-text-alt">Random delay variation (±)</span>
            </label>
            <input 
              type="number" 
              name="jitter_ms" 
              value={@toxics[:jitter_ms] || 0}
              min="0" 
              max="5000"
              class="input input-bordered input-sm"
              placeholder="0"
            />
          </div>

          <!-- Packet Loss -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Packet Loss (%)</span>
              <span class="label-text-alt">Probability of dropping packets</span>
            </label>
            <input 
              type="number" 
              name="loss_percent" 
              value={@toxics[:loss_percent] || 0}
              min="0" 
              max="100"
              class="input input-bordered input-sm"
              placeholder="0"
            />
          </div>

          <!-- Bandwidth Limit -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Bandwidth Limit (kbps)</span>
              <span class="label-text-alt">0 = unlimited</span>
            </label>
            <input 
              type="number" 
              name="bandwidth_kbps" 
              value={@toxics[:bandwidth_kbps] || 0}
              min="0"
              class="input input-bordered input-sm"
              placeholder="0"
            />
          </div>

          <!-- Apply Button -->
          <div class="card-actions justify-end">
            <button class="btn btn-primary btn-sm">
              <span class="hero-check size-4"></span>
              Apply Toxics
            </button>
          </div>
        </form>
      </div>
    </div>
    """
  end

  # helpers

  defp parse_int(nil, default), do: default
  defp parse_int("", default), do: default
  defp parse_int(v, default) when is_binary(v) do
    case Integer.parse(v) do
      {i, _} -> i
      :error -> default
    end
  end
  defp parse_int(v, _default) when is_integer(v), do: v

  defp parse_bool(v) when v in ["on", "true", true], do: true
  defp parse_bool(_), do: false
end
