defmodule MqttableWeb.ProxyTableComponent do
  @moduledoc """
  LiveComponent for displaying and managing MQTT proxies in a table format.
  
  Provides CRUD operations for proxies with inline editing capabilities,
  consistent with the connections table design.
  """
  use MqttableWeb, :live_component

  alias Mqttable.MqttProxy.Manager

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_new(:proxies, fn -> Manager.list() end)
      |> assign_new(:editing_proxy, fn -> nil end)
      |> assign_new(:show_delete_confirm, fn -> false end)
      |> assign_new(:proxy_to_delete, fn -> nil end)

    {:ok, socket}
  end

  @impl true
  def handle_event("edit_proxy", %{"name" => name}, socket) do
    {:noreply, assign(socket, :editing_proxy, name)}
  end

  @impl true
  def handle_event("cancel_edit", _params, socket) do
    {:noreply, assign(socket, :editing_proxy, nil)}
  end

  @impl true
  def handle_event("save_proxy", params, socket) do
    proxy = params_to_proxy(params)

    case Manager.upsert(proxy) do
      {:ok, _} ->
        send(self(), {:proxy_updated})
        {:noreply, assign(socket, editing_proxy: nil, proxies: Manager.list())}

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to save proxy: #{reason}")}
    end
  end

  @impl true
  def handle_event("toggle_enable", %{"name" => name, "enabled" => enabled}, socket) do
    enabled? = enabled == "true" || enabled == true
    _ = Manager.enable(name, enabled?)
    send(self(), {:proxy_updated})
    {:noreply, assign(socket, :proxies, Manager.list())}
  end

  @impl true
  def handle_event("confirm_delete", %{"name" => name}, socket) do
    {:noreply, assign(socket, show_delete_confirm: true, proxy_to_delete: name)}
  end

  @impl true
  def handle_event("cancel_delete", _params, socket) do
    {:noreply, assign(socket, show_delete_confirm: false, proxy_to_delete: nil)}
  end

  @impl true
  def handle_event("delete_proxy", %{"name" => name}, socket) do
    case Manager.stop_proxy(name) do
      :ok -> 
        # Remove from storage
        proxies = Manager.list() |> Enum.reject(&(&1.name == name))
        case Mqttable.MqttProxy.Storage.save(proxies) do
          {:ok, _} ->
            send(self(), {:proxy_updated})
            {:noreply, assign(socket, show_delete_confirm: false, proxy_to_delete: nil, proxies: Manager.list())}
          {:error, reason} ->
            {:noreply, put_flash(socket, :error, "Failed to delete proxy: #{reason}")}
        end
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to stop proxy: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("select_proxy", %{"name" => name}, socket) do
    send(self(), {:select_proxy, name})
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
          <tr>
            <th>Name</th>
            <th>Listen Address</th>
            <th>Upstream Address</th>
            <th>Status</th>
            <th>Toxics</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <%= for proxy <- @proxies do %>
            <tr class={[
              "hover:bg-base-200 cursor-pointer",
              if(@selected_proxy == proxy.name, do: "bg-primary/10", else: "")
            ]} phx-click="select_proxy" phx-target={@myself} phx-value-name={proxy.name}>
              <%= if @editing_proxy == proxy.name do %>
                <!-- Edit Mode -->
                <td colspan="6">
                  <form phx-submit="save_proxy" phx-target={@myself} class="grid grid-cols-6 gap-2 items-center">
                    <input type="hidden" name="original_name" value={proxy.name} />
                    <input name="name" value={proxy.name} class="input input-sm input-bordered" required />
                    <div class="flex gap-1">
                      <input name="listen_host" value={proxy.listen_host} class="input input-sm input-bordered w-20" />
                      <input name="listen_port" value={proxy.listen_port} class="input input-sm input-bordered w-16" />
                    </div>
                    <div class="flex gap-1">
                      <input name="upstream_host" value={proxy.upstream_host} class="input input-sm input-bordered w-20" />
                      <input name="upstream_port" value={proxy.upstream_port} class="input input-sm input-bordered w-16" />
                    </div>
                    <label class="label cursor-pointer">
                      <input type="checkbox" name="enabled" class="toggle toggle-sm" checked={proxy.enabled} />
                    </label>
                    <div class="text-xs">
                      <%= format_toxics_summary(proxy.toxics) %>
                    </div>
                    <div class="flex gap-1">
                      <button type="submit" class="btn btn-xs btn-success">Save</button>
                      <button type="button" phx-click="cancel_edit" phx-target={@myself} class="btn btn-xs btn-ghost">Cancel</button>
                    </div>
                  </form>
                </td>
              <% else %>
                <!-- View Mode -->
                <td class="font-mono font-medium"><%= proxy.name %></td>
                <td class="font-mono text-sm"><%= proxy.listen_host %>:<%= proxy.listen_port %></td>
                <td class="font-mono text-sm"><%= proxy.upstream_host %>:<%= proxy.upstream_port %></td>
                <td>
                  <div class="flex items-center gap-2">
                    <div class={[
                      "badge badge-sm",
                      if(proxy.enabled, do: "badge-success", else: "badge-error")
                    ]}>
                      <%= if proxy.enabled, do: "Enabled", else: "Disabled" %>
                    </div>
                    <%= if Manager.running?(proxy.name) do %>
                      <div class="badge badge-sm badge-primary">Running</div>
                    <% end %>
                  </div>
                </td>
                <td class="text-xs text-base-content/70">
                  <%= format_toxics_summary(proxy.toxics) %>
                </td>
                <td>
                  <div class="flex gap-1">
                    <button 
                      phx-click="toggle_enable" 
                      phx-target={@myself} 
                      phx-value-name={proxy.name} 
                      phx-value-enabled={to_string(!proxy.enabled)}
                      class={[
                        "btn btn-xs",
                        if(proxy.enabled, do: "btn-warning", else: "btn-success")
                      ]}
                    >
                      <%= if proxy.enabled, do: "Disable", else: "Enable" %>
                    </button>
                    <button 
                      phx-click="edit_proxy" 
                      phx-target={@myself} 
                      phx-value-name={proxy.name}
                      class="btn btn-xs btn-ghost"
                    >
                      <span class="hero-pencil size-3"></span>
                    </button>
                    <button 
                      phx-click="confirm_delete" 
                      phx-target={@myself} 
                      phx-value-name={proxy.name}
                      class="btn btn-xs btn-error"
                    >
                      <span class="hero-trash size-3"></span>
                    </button>
                  </div>
                </td>
              <% end %>
            </tr>
          <% end %>
          
          <%= if @proxies == [] do %>
            <tr>
              <td colspan="6" class="text-center py-8 text-base-content/50">
                <div>
                  <span class="hero-forward size-12 mx-auto block mb-2"></span>
                  <p>No proxies configured</p>
                  <p class="text-sm">Create your first proxy using the form above</p>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Delete Confirmation Modal -->
    <%= if @show_delete_confirm do %>
      <div class="modal modal-open">
        <div class="modal-box">
          <h3 class="font-bold text-lg">Delete Proxy</h3>
          <p class="py-4">
            Are you sure you want to delete the proxy "<%= @proxy_to_delete %>"? 
            This action cannot be undone and will stop the proxy if it's currently running.
          </p>
          <div class="modal-action">
            <button 
              phx-click="delete_proxy" 
              phx-target={@myself} 
              phx-value-name={@proxy_to_delete}
              class="btn btn-error"
            >
              Delete
            </button>
            <button 
              phx-click="cancel_delete" 
              phx-target={@myself}
              class="btn btn-ghost"
            >
              Cancel
            </button>
          </div>
        </div>
        <div class="modal-backdrop" phx-click="cancel_delete" phx-target={@myself}></div>
      </div>
    <% end %>
    """
  end

  # helpers

  defp params_to_proxy(params) do
    %{
      name: Map.get(params, "name"),
      listen_host: Map.get(params, "listen_host", "127.0.0.1"),
      listen_port: parse_int(params["listen_port"], 0),
      upstream_host: Map.get(params, "upstream_host", "127.0.0.1"),
      upstream_port: parse_int(params["upstream_port"], 0),
      enabled: parse_bool(params["enabled"]),
      toxics: %{
        enabled: true,
        latency_ms: 0,
        jitter_ms: 0,
        loss_percent: 0,
        bandwidth_kbps: 0
      }
    }
  end

  defp format_toxics_summary(toxics) when is_map(toxics) do
    if toxics[:enabled] do
      parts = []
      parts = if toxics[:latency_ms] > 0, do: ["#{toxics[:latency_ms]}ms lat" | parts], else: parts
      parts = if toxics[:jitter_ms] > 0, do: ["±#{toxics[:jitter_ms]}ms jit" | parts], else: parts
      parts = if toxics[:loss_percent] > 0, do: ["#{toxics[:loss_percent]}% loss" | parts], else: parts
      parts = if toxics[:bandwidth_kbps] > 0, do: ["#{toxics[:bandwidth_kbps]}kbps" | parts], else: parts
      
      case parts do
        [] -> "No toxics"
        _ -> Enum.join(Enum.reverse(parts), ", ")
      end
    else
      "Disabled"
    end
  end

  defp format_toxics_summary(_), do: "None"

  defp parse_int(nil, default), do: default
  defp parse_int("", default), do: default
  defp parse_int(v, default) when is_binary(v) do
    case Integer.parse(v) do
      {i, _} -> i
      :error -> default
    end
  end
  defp parse_int(v, _default) when is_integer(v), do: v

  defp parse_bool(v) when v in ["on", "true", true], do: true
  defp parse_bool(_), do: false
end
