defmodule MqttableWeb.Router do
  use MqttableWeb, :router

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {MqttableWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
  end

  pipeline :main do
    plug :browser
    plug :put_layout, html: {MqttableWeb.Layouts, :main}
  end

  scope "/", MqttableWeb do
    pipe_through :main

    live "/", ConnectionsLive, :index
    live "/proxy", ProxyLive, :index
    live "/demo/payload-editor", PayloadEditorDemoLive, :index
  end

  # Enable LiveDashboard in development
  if Application.compile_env(:mqttable, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :browser

      live_dashboard "/dashboard", metrics: MqttableWeb.Telemetry
    end
  end
end
